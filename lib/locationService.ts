import * as Location from 'expo-location'
import { Alert } from 'react-native'

export interface LocationResult {
  latitude: number
  longitude: number
  accuracy?: number
  method?: string
}

export interface LocationOptions {
  showProgress?: boolean
  fallbackToManual?: boolean
  defaultLocation?: { latitude: number; longitude: number }
}

/**
 * Enhanced location service with multiple accuracy levels and fallback options
 */
export class LocationService {
  
  /**
   * Request location permissions with error handling
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      // Check if Location module is available
      if (!Location || typeof Location.requestForegroundPermissionsAsync !== 'function') {
        console.warn('Location module not available')
        return false
      }

      const { status } = await Location.requestForegroundPermissionsAsync()
      return status === 'granted'
    } catch (error) {
      console.error('Error requesting location permission:', error)
      return false
    }
  }

  /**
   * Check if location services are enabled with error handling
   */
  static async isLocationEnabled(): Promise<boolean> {
    try {
      // Check if Location module is available
      if (!Location || typeof Location.hasServicesEnabledAsync !== 'function') {
        console.warn('Location services check not available')
        return false
      }

      return await Location.hasServicesEnabledAsync()
    } catch (error) {
      console.error('Error checking location services:', error)
      return false
    }
  }

  /**
   * Get current location with enhanced accuracy and fallback methods
   */
  static async getCurrentLocation(options: LocationOptions = {}): Promise<LocationResult | null> {
    const { showProgress = true, fallbackToManual = true, defaultLocation } = options

    try {
      // Check if location services are enabled
      const isLocationEnabled = await this.isLocationEnabled()
      if (!isLocationEnabled) {
        if (showProgress) {
          Alert.alert(
            'خزمەتگوزاری شوێن ناچالاکە',
            'تکایە خزمەتگوزاری شوێن (GPS) لە ڕێکخستنەکانی مۆبایلەکەت چالاک بکە.'
          )
        }
        return null
      }

      // Check permissions
      const { status } = await Location.getForegroundPermissionsAsync()
      if (status !== 'granted') {
        const hasPermission = await this.requestPermissions()
        if (!hasPermission) {
          if (showProgress) {
            Alert.alert(
              'ڕێگەپێدان نەدراوە',
              'ڕێگەپێدان بە شوێن پێویستە بۆ دیاریکردنی شوێنەکەت.'
            )
          }
          return null
        }
      }

      if (showProgress) {
        Alert.alert('دیاریکردنی شوێن', 'تکایە چەند چرکەیەک چاوەڕێ بە بۆ دیاریکردنی شوێنی ورد...')
      }

      // Try multiple location methods for better accuracy
      const locationMethods = [
        {
          name: 'GPS بەرز ورد',
          options: {
            accuracy: Location.Accuracy.BestForNavigation,
            timeout: 20000,
            maximumAge: 5000,
          },
          minAccuracy: 50
        },
        {
          name: 'GPS ناوەند',
          options: {
            accuracy: Location.Accuracy.High,
            timeout: 15000,
            maximumAge: 10000,
          },
          minAccuracy: 100
        },
        {
          name: 'شوێن تۆڕ',
          options: {
            accuracy: Location.Accuracy.Balanced,
            timeout: 10000,
            maximumAge: 15000,
          },
          minAccuracy: 500
        }
      ]

      for (const method of locationMethods) {
        try {
          const currentLocation = await Location.getCurrentPositionAsync(method.options)
          
          // Validate accuracy if specified
          const accuracy = currentLocation.coords.accuracy || 0
          if (method.minAccuracy && accuracy > method.minAccuracy) {
            console.log(`${method.name} accuracy too poor: ${accuracy}m, trying next method`)
            continue
          }

          return {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            accuracy: accuracy,
            method: method.name
          }
          
        } catch (methodError) {
          console.log(`${method.name} failed:`, methodError)
          continue
        }
      }

      // All methods failed
      throw new Error('All location methods failed')

    } catch (error) {
      console.error('Error getting location:', error)
      
      if (showProgress) {
        let errorMessage = 'نەتوانرا شوێنەکە دیاری بکرێت'
        let suggestions = ''

        const errorMsg = error instanceof Error ? error.message : String(error)
        if (errorMsg.includes('timeout')) {
          errorMessage = 'کاتەکە بەسەرچوو'
          suggestions = 'تکایە دڵنیابە لەوەی:\n• لە شوێنێکی کراوەدایت\n• GPS چالاکە\n• ئینتەرنێت بەردەستە'
        } else if (errorMsg.includes('unavailable')) {
          errorMessage = 'خزمەتگوزاری شوێن لەبەردەست نییە'
          suggestions = 'تکایە GPS لە ڕێکخستنەکانی مۆبایلەکەت چالاک بکە'
        } else if (errorMsg.includes('denied')) {
          errorMessage = 'ڕێگەپێدان بە شوێن ڕەتکراوەتەوە'
          suggestions = 'تکایە ڕێگەپێدان بە شوێن بدە لە ڕێکخستنەکانی ئەپەکە'
        }

        Alert.alert(
          'هەڵە لە دیاریکردنی شوێن',
          `${errorMessage}\n\n${suggestions}`
        )
      }

      return null
    }
  }

  /**
   * Get default location for manual mode
   */
  static getDefaultLocation(): LocationResult {
    // Using Erbil, Kurdistan Region coordinates as default
    return {
      latitude: 36.1911,
      longitude: 44.0093,
      accuracy: 0,
      method: 'شوێن بنەڕەتی'
    }
  }

  /**
   * Validate location coordinates
   */
  static isValidLocation(latitude: number, longitude: number): boolean {
    return (
      latitude >= -90 && latitude <= 90 &&
      longitude >= -180 && longitude <= 180 &&
      !isNaN(latitude) && !isNaN(longitude)
    )
  }

  /**
   * Calculate distance between two points in meters
   */
  static calculateDistance(
    lat1: number, lon1: number,
    lat2: number, lon2: number
  ): number {
    const R = 6371e3 // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180
    const φ2 = lat2 * Math.PI / 180
    const Δφ = (lat2 - lat1) * Math.PI / 180
    const Δλ = (lon2 - lon1) * Math.PI / 180

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))

    return R * c
  }

  /**
   * Format accuracy text for display
   */
  static formatAccuracy(accuracy: number): string {
    if (accuracy <= 0) return ''
    
    if (accuracy < 10) {
      return `ورد زۆر (${Math.round(accuracy)}م)`
    } else if (accuracy < 50) {
      return `ورد (${Math.round(accuracy)}م)`
    } else if (accuracy < 100) {
      return `ناوەند (${Math.round(accuracy)}م)`
    } else {
      return `کەم ورد (${Math.round(accuracy)}م)`
    }
  }
}
