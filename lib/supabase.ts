import { createClient } from '@supabase/supabase-js'
import { Database } from '../types/supabase'

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://emxbbmsieclwlslwgkua.supabase.co'
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  // Don't throw error, use fallback values
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  }
})
