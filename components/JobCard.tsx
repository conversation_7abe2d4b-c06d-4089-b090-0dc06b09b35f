import React, { memo, useCallback, useMemo } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  Alert,
  Linking,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { Job, UserRole } from '../types/common'
import WorkingImage from './WorkingImage'

interface JobCardProps {
  job: Job
  userRole: UserRole
  onEdit?: (job: Job) => void
  onDelete?: (jobId: string) => void
  onComplete?: (jobId: string) => void
  onPin?: (jobId: string, isPinned: boolean) => void
  onImagePress?: (imageUri: string) => void
  style?: ViewStyle
  showActions?: boolean
}

const JobCard: React.FC<JobCardProps> = memo(({
  job,
  userRole,
  onEdit,
  onDelete,
  onComplete,
  onPin,
  onImagePress,
  style,
  showActions = true,
}) => {
  const handleCall = useCallback(() => {
    if (job.customer_phone) {
      Linking.openURL(`tel:${job.customer_phone}`)
    }
  }, [job.customer_phone])

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(job)
    }
  }, [onEdit, job])

  const handleDelete = useCallback(() => {
    if (onDelete) {
      Alert.alert(
        'سڕینەوەی کار',
        'دڵنیایت لە سڕینەوەی ئەم کارە؟',
        [
          { text: 'نەخێر', style: 'cancel' },
          {
            text: 'بەڵێ',
            style: 'destructive',
            onPress: () => onDelete(job.id),
          },
        ]
      )
    }
  }, [onDelete, job.id])

  const handleComplete = useCallback(() => {
    if (onComplete) {
      Alert.alert(
        'تەواوکردنی کار',
        'دڵنیایت لە تەواوکردنی ئەم کارە؟',
        [
          { text: 'نەخێر', style: 'cancel' },
          {
            text: 'بەڵێ',
            onPress: () => onComplete(job.id),
          },
        ]
      )
    }
  }, [onComplete, job.id])

  const handlePin = useCallback(() => {
    if (onPin) {
      onPin(job.id, !job.is_pinned)
    }
  }, [onPin, job.id, job.is_pinned])

  const actionButtons = useMemo(() => {
    if (!showActions) return null

    const buttons = []

    // Admin actions
    if (userRole === 'admin') {
      if (onPin) {
        buttons.push(
          <TouchableOpacity
            key="pin"
            style={[styles.actionButton, styles.pinButton]}
            onPress={handlePin}
          >
            <Ionicons
              name={job.is_pinned ? 'bookmark' : 'bookmark-outline'}
              size={16}
              color="#6B7280"
            />
          </TouchableOpacity>
        )
      }

      if (onEdit) {
        buttons.push(
          <TouchableOpacity
            key="edit"
            style={[styles.actionButton, styles.editButton]}
            onPress={handleEdit}
          >
            <Ionicons name="create-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        )
      }

      if (onDelete) {
        buttons.push(
          <TouchableOpacity
            key="delete"
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDelete}
          >
            <Ionicons name="trash-outline" size={16} color="#EF4444" />
          </TouchableOpacity>
        )
      }
    }

    // Installer actions
    if ((userRole === 'installer1' || userRole === 'installer2') && onComplete && !job.is_completed) {
      buttons.push(
        <TouchableOpacity
          key="complete"
          style={[styles.actionButton, styles.completeButton]}
          onPress={handleComplete}
        >
          <Ionicons name="checkmark-circle" size={16} color="white" />
          <Text style={styles.completeButtonText}>تەواو</Text>
        </TouchableOpacity>
      )
    }

    return (
      <View style={styles.actionButtonsContainer}>
        {buttons}
      </View>
    )
  }, [showActions, userRole, onPin, onEdit, onDelete, onComplete, handlePin, handleEdit, handleDelete, handleComplete, job.is_pinned, job.is_completed])

  const renderImages = () => {
    if (!job.images || job.images.length === 0) return null

    return (
      <View style={styles.imagesContainer}>
        <View style={styles.imagesHeader}>
          <Ionicons name="images" size={16} color="#6C757D" />
          <Text style={styles.imagesLabel}>{job.images.length} وێنە</Text>
        </View>
        <View style={styles.imagesList}>
          {job.images.slice(0, 3).map((image, index) => (
            <WorkingImage
              key={index}
              source={{ uri: image }}
              style={styles.thumbImage}
              containerStyle={styles.imageThumb}
              onPress={() => onImagePress?.(image)}
            />
          ))}
          {job.images.length > 3 && (
            <View style={styles.moreImagesIndicator}>
              <Text style={styles.moreImagesText}>+{job.images.length - 3}</Text>
            </View>
          )}
        </View>
      </View>
    )
  }

  return (
    <View style={[styles.card, style]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.customerInfo}>
          <View style={styles.customerNameRow}>
            <Ionicons name="person" size={18} color="#333" />
            <Text style={styles.customerName}>{job.customer_name}</Text>
          </View>
          
          <TouchableOpacity style={styles.phoneRow} onPress={handleCall}>
            <Ionicons name="call" size={16} color="#007AFF" />
            <Text style={styles.customerPhone}>{job.customer_phone}</Text>
          </TouchableOpacity>

          {job.referral_source_name && (
            <View style={styles.referralRow}>
              <Ionicons name="people" size={16} color="#17a2b8" />
              <Text style={styles.referralSource}>
                لە لایەن: {job.referral_source_name}
              </Text>
            </View>
          )}

          <View style={styles.locationRow}>
            <Ionicons name="location" size={16} color="#FF6B35" />
            <Text style={styles.location}>
              {job.place_name}{job.area_name ? ` - ${job.area_name}` : ''}
            </Text>
          </View>
        </View>

        {actionButtons}
      </View>

      {/* Status Badge */}
      {job.is_completed && (
        <View style={styles.statusBadge}>
          <Ionicons name="checkmark-circle" size={14} color="white" />
          <Text style={styles.statusText}>تەواو</Text>
        </View>
      )}

      {/* Images */}
      {renderImages()}

      {/* Date */}
      <View style={styles.dateContainer}>
        <Ionicons name="calendar" size={14} color="#6C757D" />
        <Text style={styles.dateText}>
          {new Date(job.created_at).toLocaleDateString('ku')}
        </Text>
      </View>
    </View>
  )
})

JobCard.displayName = 'JobCard'

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderRightWidth: 4,
    borderRightColor: '#007AFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  customerInfo: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 8,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
    textAlign: 'right',
  },
  phoneRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerPhone: {
    fontSize: 16,
    color: '#007AFF',
    marginRight: 6,
    textAlign: 'right',
  },
  referralRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 4,
  },
  referralSource: {
    fontSize: 14,
    color: '#17a2b8',
    marginRight: 6,
    textAlign: 'right',
  },
  locationRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  location: {
    fontSize: 14,
    color: '#FF6B35',
    marginRight: 6,
    textAlign: 'right',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pinButton: {
    backgroundColor: '#F3F4F6',
  },
  editButton: {
    backgroundColor: '#F3F4F6',
  },
  deleteButton: {
    backgroundColor: '#FEF2F2',
  },
  completeButton: {
    backgroundColor: '#10B981',
    flexDirection: 'row',
    paddingHorizontal: 12,
    gap: 4,
  },
  completeButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  imagesContainer: {
    marginBottom: 12,
  },
  imagesHeader: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 8,
  },
  imagesLabel: {
    fontSize: 14,
    color: '#6C757D',
    marginRight: 6,
  },
  imagesList: {
    flexDirection: 'row-reverse',
    gap: 8,
  },
  imageThumb: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  thumbImage: {
    width: '100%',
    height: '100%',
  },
  moreImagesIndicator: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreImagesText: {
    fontSize: 12,
    color: '#6C757D',
    fontWeight: '600',
  },
  dateContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  dateText: {
    fontSize: 14,
    color: '#6C757D',
    marginRight: 6,
  },
})

export default JobCard
