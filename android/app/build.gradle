apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
// Removed React Native Gradle Plugin for CLI-based auto-linking compatibility

// React Native CLI auto-linking (compatible with legacy packages)
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)

// React Native configuration removed - using CLI-based auto-linking instead

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = (findProperty('android.enableProguardInReleaseBuilds') ?: false).toBoolean()

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Whether to enable the Hermes VM.
 */
def hermesEnabled = (findProperty('expo.jsEngine') ?: "hermes") == "hermes"

/**
 * Flipper version for debugging
 */
def FLIPPER_VERSION = findProperty('FLIPPER_VERSION') ?: '0.125.0'

android {
    ndkVersion rootProject.ext.ndkVersion
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    namespace 'com.aliduct.manager'

    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        applicationId 'com.aliduct.manager'
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0.0"
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.debug
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging (findProperty('expo.useLegacyPackaging')?.toBoolean() ?: false)
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"

    // React Native dependencies (CLI-based auto-linking)
    implementation "com.facebook.react:react-android:0.73.9"

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android:0.73.9")
    } else {
        implementation jscFlavor
    }
}
