// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  ext {
    buildToolsVersion = "34.0.0"
    minSdkVersion = 21
    compileSdkVersion = 34
    targetSdkVersion = 34
    ndkVersion = "25.1.8937393"
    kotlinVersion = "1.9.24"
  }
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath('com.android.tools.build:gradle:8.6.1')
    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24')
    classpath('com.facebook.react:react-native-gradle-plugin')
  }
}

allprojects {
  repositories {
    google()
    mavenCentral()
    maven { url 'https://www.jitpack.io' }
    maven { url "https://maven.google.com" }
    flatDir {
      dirs '../node_modules/react-native/android'
    }
  }
}

subprojects {
  afterEvaluate { project ->
    if (project.hasProperty("android")) {
      android {
        buildFeatures {
          buildConfig true
        }
      }
    }
  }
}
