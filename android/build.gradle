// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  ext {
    buildToolsVersion = "34.0.0"
    minSdkVersion = 21
    compileSdkVersion = 34
    targetSdkVersion = 34
    ndkVersion = "25.1.8937393"
    kotlinVersion = "1.9.24"
  }
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath('com.android.tools.build:gradle:8.6.1')
    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24')
    // Removed React Native Gradle Plugin for CLI-based auto-linking compatibility
  }
}

allprojects {
  repositories {
    google()
    mavenCentral()
    maven { url 'https://www.jitpack.io' }
    maven { url "https://maven.google.com" }

    // React Native repositories (CLI-based auto-linking)
    maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
    maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url("$rootDir/../node_modules/react-native/android")
    }
  }
}

subprojects {
  afterEvaluate { project ->
    if (project.hasProperty("android")) {
      android {
        buildFeatures {
          buildConfig true
        }
        compileOptions {
          sourceCompatibility JavaVersion.VERSION_1_8
          targetCompatibility JavaVersion.VERSION_1_8
        }
      }
    }

    // Force all Kotlin compilation to use JVM 1.8
    project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
      kotlinOptions {
        jvmTarget = "1.8"
      }
    }

    // Force React Native dependency versions
    project.configurations.all {
      resolutionStrategy {
        force 'com.facebook.react:react-android:0.73.9'
        force 'com.facebook.react:hermes-android:0.73.9'
      }
    }
  }
}
