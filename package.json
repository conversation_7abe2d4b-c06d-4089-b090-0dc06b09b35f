{"name": "aliduct", "displayName": "Ali duct factory", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:local": "expo run:android --device", "build:apk": "eas build --platform android --profile preview", "preview": "eas build --profile preview", "update": "eas update", "doctor": "expo doctor", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.1.0", "@expo/vector-icons": "^14.1.0", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^2.50.0", "base64-arraybuffer": "^1.0.2", "expo": "53.0.15", "expo-camera": "~16.1.10", "expo-crypto": "~14.1.5", "expo-image-picker": "^16.1.4", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-notifications": "~0.31.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "react": "18.2.0", "react-native": "0.73.9", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-webview": "13.13.5", "stream-browserify": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.0", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["stream-browserify"], "listUnknownPackages": false}}}, "private": true, "overrides": {"react": "18.2.0", "@types/react": "~18.2.0"}, "resolutions": {"react": "18.2.0", "@types/react": "~18.2.0"}}