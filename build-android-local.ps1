#!/usr/bin/env pwsh

# Ali Duct Factory - Local Android Build Script
# Optimized for D:\.gradle cache usage

Write-Host "🚀 Ali Duct Factory - Local Android Build" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Set environment variables to use D drive Gradle cache
$env:GRADLE_USER_HOME = "D:\.gradle"
$env:GRADLE_CACHE_DIR = "D:\.gradle\caches"
$env:GRADLE_WRAPPER_DIR = "D:\.gradle\wrapper"

Write-Host "📁 Using Gradle cache: $env:GRADLE_USER_HOME" -ForegroundColor Cyan

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found. Please install Java 17" -ForegroundColor Red
    exit 1
}

# Check Android SDK
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ ANDROID_HOME not set. Please set up Android SDK" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Android SDK: $env:ANDROID_HOME" -ForegroundColor Green
}

# Check for connected device or emulator
Write-Host "📱 Checking for Android device..." -ForegroundColor Yellow
$devices = adb devices | Select-String "device$"
if ($devices.Count -eq 0) {
    Write-Host "⚠️  No Android device detected. Please connect a device or start an emulator." -ForegroundColor Yellow
    Write-Host "   You can still build the APK and install it manually." -ForegroundColor Cyan
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
    Write-Host "✅ Cleaned Android build directory" -ForegroundColor Green
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Dependencies installed" -ForegroundColor Green

# Build APK
Write-Host "🔨 Building Android APK..." -ForegroundColor Yellow
npx expo run:android --device --no-build-cache

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    
    # Look for APK file
    $apkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        Write-Host "📱 APK created: $apkPath" -ForegroundColor Green
        
        # Try to install if device is connected
        if ($devices.Count -gt 0) {
            Write-Host "📲 Installing APK to device..." -ForegroundColor Cyan
            adb install -r $apkPath
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ APK installed successfully!" -ForegroundColor Green
                Write-Host "🎯 You can now launch 'Ali Duct Factory' on your device" -ForegroundColor Green
            } else {
                Write-Host "⚠️  APK built but installation failed. You can install manually." -ForegroundColor Yellow
            }
        } else {
            Write-Host "📋 To install manually:" -ForegroundColor Cyan
            Write-Host "   1. Copy $apkPath to your Android device" -ForegroundColor Cyan
            Write-Host "   2. Enable 'Install from unknown sources' in Android settings" -ForegroundColor Cyan
            Write-Host "   3. Install the APK file" -ForegroundColor Cyan
        }
    } else {
        Write-Host "⚠️  Build completed but APK not found at expected location" -ForegroundColor Yellow
        Write-Host "🔍 Searching for APK files..." -ForegroundColor Yellow
        Get-ChildItem -Path "android" -Filter "*.apk" -Recurse | ForEach-Object {
            Write-Host "   Found: $($_.FullName)" -ForegroundColor Cyan
        }
    }
} else {
    Write-Host "❌ Build failed. Check the error messages above." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🏁 Build process completed!" -ForegroundColor Green
