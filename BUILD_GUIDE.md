# Ali Duct Factory - Build Guide

## Quick Build for Android

### Prerequisites
- Java 17 installed and JAVA_HOME set
- Android SDK with ANDROID_HOME environment variable
- Android device connected with USB debugging enabled

### Optimized Local Build

Use the provided PowerShell script for the fastest build experience:

```powershell
.\build-android-local.ps1
```

**What this script does:**
- ✅ Uses your existing D:\.gradle cache (saves time and storage)
- ✅ Automatically installs npm dependencies
- ✅ Builds optimized APK with proper settings
- ✅ Auto-installs to connected Android device
- ✅ Provides manual installation guide if no device connected

### Manual Build Steps

If you prefer manual control:

1. **Set environment variables:**
   ```bash
   set GRADLE_USER_HOME=D:\.gradle
   set GRADLE_CACHE_DIR=D:\.gradle\caches
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Build APK:**
   ```bash
   npx expo run:android --device
   ```

### Build Optimizations

The project is configured with:
- **Gradle Cache**: Uses D:\.gradle to avoid duplicate downloads
- **Parallel Builds**: Enabled for faster compilation
- **R8 Full Mode**: Smaller APK size
- **Build Cache**: Faster subsequent builds
- **Memory Optimization**: 4GB heap for large projects

### Troubleshooting

**Build fails with "Java not found":**
- Install Java 17 and set JAVA_HOME environment variable

**"ANDROID_HOME not set" error:**
- Install Android SDK and set ANDROID_HOME to SDK location

**Gradle cache issues:**
- The script automatically uses D:\.gradle cache
- If issues persist, delete D:\.gradle\caches and rebuild

**Device not detected:**
- Enable USB debugging on Android device
- Install device drivers if needed
- Use `adb devices` to verify connection

### APK Location

After successful build, find your APK at:
```
android/app/build/outputs/apk/debug/app-debug.apk
```

### Installation

**Automatic (if device connected):**
The script will automatically install the APK to your connected device.

**Manual installation:**
1. Copy APK to your Android device
2. Enable "Install from unknown sources" in Android settings
3. Install the APK file

## Performance Tips

- Keep D:\.gradle cache for faster subsequent builds
- Use connected device instead of emulator for faster testing
- Close unnecessary applications during build to free up memory
- Use SSD storage for better build performance

## Build Variants

- **Debug**: Default build with debugging enabled
- **Release**: Optimized build for production (use EAS build for release)

For production releases, use:
```bash
npm run build:apk
```

This will create a production-ready APK via EAS Build.
