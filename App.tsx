import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Platform, LogBox } from 'react-native';
import { AuthProvider } from './contexts/AuthContext';
import AppNavigator from './navigation/AppNavigator';
import ErrorBoundary from './components/ErrorBoundary';

// Ignore specific warnings that might cause crashes
LogBox.ignoreLogs([
  'Warning: componentWillReceiveProps',
  'Warning: componentWillMount',
  'Module RCTImageLoader',
  'Setting a timer for a long period of time',
  'VirtualizedLists should never be nested',
  'Require cycle:',
]);

export default function App() {
  useEffect(() => {
    // Log app startup for debugging
    console.log('App starting up...');
    console.log('Platform:', Platform.OS);
    console.log('Environment variables loaded:', {
      hasSupabaseUrl: !!process.env.EXPO_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
    });
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppNavigator />
        <StatusBar style="auto" />
      </AuthProvider>
    </ErrorBoundary>
  );
}
