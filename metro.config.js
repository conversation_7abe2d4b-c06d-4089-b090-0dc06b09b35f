/**
 * Metro configuration for React Native
 * https://github.com/facebook/metro
 *
 * @format
 */

const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  resolver: {
    alias: {
      stream: 'stream-browserify',
      buffer: '@craftzdog/react-native-buffer',
    },
    platforms: ['native', 'android', 'ios', 'web'],
  },
};

module.exports = mergeConfig(defaultConfig, config);
