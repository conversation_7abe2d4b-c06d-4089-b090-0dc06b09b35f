/**
 * Metro configuration for React Native
 * https://github.com/facebook/metro
 *
 * @format
 */

module.exports = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  resolver: {
    alias: {
      stream: 'stream-browserify',
      buffer: '@craftzdog/react-native-buffer',
    },
    platforms: ['native', 'android', 'ios', 'web'],
  },
};
