import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react'
import * as SecureStore from 'expo-secure-store'
import { supabase } from '../lib/supabase'
import { User, UserRole } from '../types/common'

interface AuthContextType {
  user: User | null
  login: (username: string, password: string, stayLoggedIn?: boolean) => Promise<boolean>
  logout: () => Promise<void>
  loading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Simple password verification (in production, use proper hashing)
const verifyPassword = (inputPassword: string, username: string): boolean => {
  const credentials: Record<string, string> = {
    'ali': '223344',
    'car': '123455',
    'wasta1': '654321',
    'wasta2': '123456788'
  }
  return credentials[username] === inputPassword
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkStoredAuth()
  }, [])

  const checkStoredAuth = useCallback(async () => {
    try {
      const storedUser = await SecureStore.getItemAsync('user')
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser) as User
        setUser(parsedUser)
      }
    } catch (error) {
      console.error('Error checking stored auth:', error)
      // Clear corrupted data
      try {
        await SecureStore.deleteItemAsync('user')
      } catch (deleteError) {
        console.warn('Failed to clear corrupted auth data:', deleteError)
      }
    } finally {
      setLoading(false)
    }
  }, [])

  const login = useCallback(async (username: string, password: string, stayLoggedIn = false): Promise<boolean> => {
    try {
      // Input validation
      if (!username?.trim() || !password?.trim()) {
        return false
      }

      // Verify password
      if (!verifyPassword(password, username)) {
        return false
      }

      // Get user from database with error handling
      let userData = null
      try {
        const { data, error } = await supabase
          .from('app_users')
          .select('*')
          .eq('username', username.trim())
          .single()

        if (error) {
          console.error('Database query error:', error)
          return false
        }
        userData = data
      } catch (networkError) {
        console.error('Network error during login:', networkError)
        return false
      }

      if (!userData) {
        console.error('User not found in database')
        return false
      }

      const userObj: User = {
        id: userData.id,
        username: userData.username,
        role: userData.role as UserRole,
        display_name: userData.display_name
      }

      setUser(userObj)

      // Store user if stay logged in is enabled
      if (stayLoggedIn) {
        await SecureStore.setItemAsync('user', JSON.stringify(userObj))
      }

      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }, [])

  const logout = useCallback(async () => {
    try {
      await SecureStore.deleteItemAsync('user')
      setUser(null)
    } catch (error) {
      console.error('Logout error:', error)
      // Force logout even if SecureStore fails
      setUser(null)
    }
  }, [])

  // Memoize computed values
  const isAuthenticated = useMemo(() => !!user, [user])

  const contextValue = useMemo(() => ({
    user,
    login,
    logout,
    loading,
    isAuthenticated
  }), [user, login, logout, loading, isAuthenticated])

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}
